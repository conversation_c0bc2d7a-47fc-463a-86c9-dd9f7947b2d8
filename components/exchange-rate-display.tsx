"use client"

import { useEffect, useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { TrendingUp, TrendingDown, Minus, Search, Star, StarOff } from "lucide-react"
import { useLanguage } from "./language-provider"

interface ExchangeRate {
  id: number
  symbol: string
  rate: number
  change: number
  flag: string
  isFavorite: boolean
  askPrice?: number | null
  bidPrice?: number | null
  lastUpdated?: string | null
}

export function ExchangeRateDisplay() {
  const [rates, setRates] = useState<ExchangeRate[]>([])
  const [allRates, setAllRates] = useState<ExchangeRate[]>([])
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [showFavori<PERSON>On<PERSON>, set<PERSON><PERSON><PERSON>avoritesOnly] = useState(true)
  const [loading, setLoading] = useState(true)
  const [isClient, setIsClient] = useState(false)
  const { t } = useLanguage()

  // Fetch currencies from API
  const fetchCurrencies = async (favorites: boolean = false, query: string = "") => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (favorites) params.append('favorites', 'true')
      if (query) params.append('q', query)
      params.append('limit', '50')

      const response = await fetch(`/api/currencies?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        return data.data
      } else {
        console.error('Failed to fetch currencies:', data.error)
        return []
      }
    } catch (error) {
      console.error('Error fetching currencies:', error)
      return []
    } finally {
      setLoading(false)
    }
  }

  // Toggle favorite status
  const toggleFavorite = async (currencyId: number, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/currencies/${currencyId}/favorite`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isFavorite: !currentStatus }),
      })

      const data = await response.json()
      if (data.success) {
        // Update local state
        setRates(prev => prev.map(rate => 
          rate.id === currencyId 
            ? { ...rate, isFavorite: !currentStatus }
            : rate
        ))
        setAllRates(prev => prev.map(rate => 
          rate.id === currencyId 
            ? { ...rate, isFavorite: !currentStatus }
            : rate
        ))
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
    }
  }

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true)
    setLastUpdated(new Date())
  }, [])

  // Load initial data
  useEffect(() => {
    if (!isClient) return // Wait for client-side hydration

    const loadData = async () => {
      const [favorites, all] = await Promise.all([
        fetchCurrencies(true),
        fetchCurrencies(false)
      ])
      
      setRates(favorites)
      setAllRates(all)
      setLastUpdated(new Date())
    }

    loadData()

    // Set up auto-refresh every 60 seconds
    const interval = setInterval(() => {
      loadData()
    }, 60000)

    return () => clearInterval(interval)
  }, [isClient])

  // Handle search and filter changes
  useEffect(() => {
    if (searchQuery.trim()) {
      // Filter all rates by search query
      const filtered = allRates.filter(rate =>
        rate.symbol.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setRates(filtered)
      setShowFavoritesOnly(false)
    } else if (showFavoritesOnly) {
      // Show only favorites
      setRates(allRates.filter(rate => rate.isFavorite))
    } else {
      // Show all rates
      setRates(allRates)
    }
  }, [searchQuery, showFavoritesOnly, allRates])

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-3 w-3 text-green-600" />
    if (change < 0) return <TrendingDown className="h-3 w-3 text-red-600" />
    return <Minus className="h-3 w-3 text-muted-foreground" />
  }

  const getTrendColor = (change: number) => {
    if (change > 0) return "text-green-600"
    if (change < 0) return "text-red-600"
    return "text-muted-foreground"
  }

  return (
    <section id="rates" className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2 drop-shadow-lg">{t("exchangeRates")}</h2>
        <p className="text-sm text-muted-foreground">
          {t("lastUpdated")}: {lastUpdated ? lastUpdated.toLocaleTimeString() : '--:--:--'}
        </p>
      </div>

      {/* Search and Filter Controls */}
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder={t("searchCurrencies")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={showFavoritesOnly ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setShowFavoritesOnly(true)
              setSearchQuery("")
            }}
            className="flex items-center gap-2"
          >
            <Star className="h-4 w-4" />
            {t("favorites")}
          </Button>
          <Button
            variant={!showFavoritesOnly && !searchQuery ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setShowFavoritesOnly(false)
              setSearchQuery("")
            }}
          >
            {t("allCurrencies")}
          </Button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">{t("loading")}</p>
        </div>
      )}

      {/* Currency Cards - Scrollable Container */}
      <div className="max-h-[600px] overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent">
        {!loading && rates.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {searchQuery
                ? t("noSearchResults")
                : showFavoritesOnly
                  ? t("noFavorites")
                  : t("noCurrencies")
              }
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 pr-2">
          {rates.map((rate) => (
            <Card
              key={rate.id}
              className="hover:shadow-lg transition-all duration-300 bg-card/80 backdrop-blur-sm border-border hover:border-primary/50 hover:glow-effect"
            >
              <CardHeader className="pb-2 px-4 pt-4">
                <CardTitle className="flex items-center justify-between text-sm text-foreground">
                  <div className="flex items-center space-x-2">
                    <div className="flex flex-col">
                      <span className="text-sm font-semibold">
                        {rate.symbol}
                      </span>
                      <span className="text-xs text-muted-foreground font-normal">
                        {rate.lastUpdated && new Date(rate.lastUpdated).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      // onClick={() => toggleFavorite(rate.id, rate.isFavorite)}
                      className="p-1 h-6 w-6"
                    >
                      {rate.isFavorite ? (
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      ) : (
                        <StarOff className="h-3 w-3 text-muted-foreground" />
                      )}
                    </Button>
                    {getTrendIcon(rate.change)}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="px-4 pb-4">
                <div className="space-y-2">
                  <div className="text-lg font-bold text-primary">
                    {rate.rate.toFixed(rate.symbol.includes('JPY') ? 2 : 4)}
                  </div>
                  <div className="flex items-center justify-between">
                    <Badge
                      variant={rate.change >= 0 ? "default" : "destructive"}
                      className="text-xs bg-primary hover:bg-primary/80"
                    >
                      {rate.change >= 0 ? "+" : ""}
                      {rate.change.toFixed(2)}%
                    </Badge>
                    <span className={`text-xs font-medium ${getTrendColor(rate.change)}`}>
                      {rate.change >= 0 ? "+" : ""}
                      {((rate.rate * rate.change) / 100).toFixed(rate.symbol.includes('JPY') ? 3 : 5)}
                    </span>
                  </div>
                  {(rate.askPrice || rate.bidPrice) && (
                    <div className="flex justify-between text-xs text-muted-foreground pt-1 border-t">
                      {rate.bidPrice && <span>{t("bid")}: {rate.bidPrice.toFixed(4)}</span>}
                      {rate.askPrice && <span>{t("ask")}: {rate.askPrice.toFixed(4)}</span>}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
